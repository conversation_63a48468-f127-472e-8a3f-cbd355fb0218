// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, { useCallback, useEffect, useRef, useState } from "react";
import {
    View,
    Text,
    TouchableOpacity,
    ScrollView,
    StyleSheet,
    Platform,
} from "react-native";
import type {
    LayoutChangeEvent,
    NativeSyntheticEvent,
    NativeScrollEvent,
} from "react-native";


import CompassIcon from "@components/compass_icon";
import { useTheme } from "@context/theme";
import { changeOpacity, makeStyleSheetFromTheme } from "@utils/theme";

import type { SelectedEmoji } from "@components/post_draft/emoji_preview";

type Props = {
    selectedEmojis: SelectedEmoji[];
    onRemoveEmoji: (id: string) => void;
    onDone: () => void;
    testID?: string;
};

const getStyleSheet = makeStyleSheetFromTheme((theme) => {
    return StyleSheet.create({
        container: {
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.04),
            borderRadius: 12,
            borderBottomColor: changeOpacity(theme.centerChannelColor, 0.08),
            paddingHorizontal: 16,
            paddingVertical: 10,
            maxHeight: 100,
            minHeight: 100,
        },
        header: {
            flexDirection: "row",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: 8,
        },
        title: {
            fontSize: 14,
            fontWeight: "600",
            color: theme.centerChannelColor,
        },
        doneButton: {
            backgroundColor: theme.buttonBg,
            paddingHorizontal: 16,
            paddingVertical: 6,
            borderRadius: 16,
        },
        doneButtonText: {
            color: theme.buttonColor,
            fontSize: 14,
            fontWeight: "600",
        },
        scrollContainer: {
            flex: 1,
        },
        scrollContent: {
            flexDirection: "row",
            alignItems: "center",
            paddingVertical: 4,
            paddingHorizontal: 16,
        },
        emptyText: {
            color: changeOpacity(theme.centerChannelColor, 0.6),
            fontSize: 12,
            fontStyle: "italic",
            textAlign: "center",
            flex: 1,
            paddingVertical: 8,
        },
        emojiItem: {
            flexDirection: "row",
            alignItems: "center",
            backgroundColor: changeOpacity(theme.buttonBg, 0.12),
            borderRadius: 16,
            paddingHorizontal: 8,
            paddingVertical: 4,
            marginRight: 8,
            borderWidth: 1,
            borderColor: changeOpacity(theme.buttonBg, 0.25),
            minWidth: 50,
            flexShrink: 0,
        },
        emojiCharacter: {
            fontSize: 16,
            marginRight: 4,
        },
        removeButton: {
            padding: 2,
            borderRadius: 8,
            backgroundColor: changeOpacity(theme.centerChannelColor, 0.1),
            minWidth: 20,
            minHeight: 20,
            alignItems: "center",
            justifyContent: "center",
        },
        removeIcon: {
            color: changeOpacity(theme.centerChannelColor, 0.7),
        },
        debugText: {
            fontSize: 10,
            color: changeOpacity(theme.centerChannelColor, 0.6),
            textAlign: "center",
            marginTop: 4,
        },
    });
});

const EmojiPickerPreview = ({
    selectedEmojis,
    onRemoveEmoji,
    onDone,
    testID = "emoji_picker_preview",
}: Props) => {
    const theme = useTheme();
    const styles = getStyleSheet(theme);
    const scrollViewRef = useRef<ScrollView>(null);

    // Diagnostic state
    const [scrollDiagnostics, setScrollDiagnostics] = useState({
        containerWidth: 0,
        contentWidth: 0,
        scrollPosition: 0,
        scrollEnabled: true,
        canScroll: false,
    });



    // Diagnostic logging functions
    const logScrollEvent = useCallback((eventName: string, data?: any) => {
        console.log(`[EmojiPickerPreview] ${eventName}:`, {
            timestamp: Date.now(),
            selectedEmojisCount: selectedEmojis.length,
            diagnostics: scrollDiagnostics,
            ...data,
        });
    }, [selectedEmojis.length, scrollDiagnostics]);

    const handleRemoveEmoji = useCallback(
        (id: string) => {
            logScrollEvent('Remove Emoji', { emojiId: id });
            onRemoveEmoji(id);
        },
        [onRemoveEmoji, logScrollEvent]
    );

    const handleDone = useCallback(() => {
        logScrollEvent('Done Pressed');
        onDone();
    }, [onDone, logScrollEvent]);

    // Scroll event handlers with diagnostics
    const handleScroll = useCallback((event: NativeSyntheticEvent<NativeScrollEvent>) => {
        const { contentOffset, contentSize, layoutMeasurement } = event.nativeEvent;
        const newDiagnostics = {
            containerWidth: layoutMeasurement.width,
            contentWidth: contentSize.width,
            scrollPosition: contentOffset.x,
            scrollEnabled: true,
            canScroll: contentSize.width > layoutMeasurement.width,
        };

        setScrollDiagnostics(newDiagnostics);
        logScrollEvent('Scroll Event', {
            contentOffset: contentOffset.x,
            contentWidth: contentSize.width,
            containerWidth: layoutMeasurement.width,
            canScroll: newDiagnostics.canScroll,
        });
    }, [logScrollEvent]);

    const handleScrollBeginDrag = useCallback(() => {
        logScrollEvent('Scroll Begin Drag');
    }, [logScrollEvent]);

    const handleScrollEndDrag = useCallback(() => {
        logScrollEvent('Scroll End Drag');
    }, [logScrollEvent]);

    const handleMomentumScrollBegin = useCallback(() => {
        logScrollEvent('Momentum Scroll Begin');
    }, [logScrollEvent]);

    const handleMomentumScrollEnd = useCallback(() => {
        logScrollEvent('Momentum Scroll End');
    }, [logScrollEvent]);

    const handleContainerLayout = useCallback((event: LayoutChangeEvent) => {
        const { width } = event.nativeEvent.layout;
        logScrollEvent('Container Layout', { containerWidth: width });
        setScrollDiagnostics(prev => ({ ...prev, containerWidth: width }));
    }, [logScrollEvent]);

    const handleContentSizeChange = useCallback((width: number, height: number) => {
        logScrollEvent('Content Size Change', { contentWidth: width, contentHeight: height });
        setScrollDiagnostics(prev => ({
            ...prev,
            contentWidth: width,
            canScroll: width > prev.containerWidth
        }));
    }, [logScrollEvent]);

    // Systematic testing function
    const runScrollTests = useCallback(() => {
        logScrollEvent('Running Scroll Tests');

        if (!scrollViewRef.current) {
            logScrollEvent('Test Failed: ScrollView ref is null');
            return;
        }

        // Test 1: Check if ScrollView is enabled
        logScrollEvent('Test 1: ScrollView Enabled Check', {
            scrollEnabled: scrollDiagnostics.scrollEnabled
        });

        // Test 2: Check content vs container size
        logScrollEvent('Test 2: Size Comparison', {
            containerWidth: scrollDiagnostics.containerWidth,
            contentWidth: scrollDiagnostics.contentWidth,
            shouldScroll: scrollDiagnostics.contentWidth > scrollDiagnostics.containerWidth,
            canScroll: scrollDiagnostics.canScroll,
        });

        // Test 3: Try programmatic scroll
        setTimeout(() => {
            logScrollEvent('Test 3: Programmatic Scroll Test');
            scrollViewRef.current?.scrollTo({ x: 50, animated: true });
        }, 1000);

        // Test 4: Try scroll to end
        setTimeout(() => {
            logScrollEvent('Test 4: Scroll to End Test');
            scrollViewRef.current?.scrollToEnd({ animated: true });
        }, 2000);

        // Test 5: Check scroll position after programmatic scroll
        setTimeout(() => {
            logScrollEvent('Test 5: Final Position Check', {
                finalPosition: scrollDiagnostics.scrollPosition,
            });
        }, 3000);
    }, [scrollDiagnostics, logScrollEvent]);

    // Auto-scroll to the end when new emojis are selected
    useEffect(() => {
        logScrollEvent('Selected Emojis Changed', { count: selectedEmojis.length });
        if (scrollViewRef.current) {
            scrollViewRef.current.scrollToEnd({ animated: true });
        }

        // Run tests when we have multiple emojis
        if (selectedEmojis.length >= 3) {
            setTimeout(() => runScrollTests(), 500);
        }
    }, [selectedEmojis.length, logScrollEvent, runScrollTests]);



    const scrollConfig = {
        ios: {
            nestedScrollEnabled: true,
            scrollEventThrottle: 16,
            decelerationRate: "fast" as const,
            bounces: true,
            alwaysBounceHorizontal: true,
            alwaysBounceVertical: false,
        },
        android: {
            nestedScrollEnabled: true,
            scrollEventThrottle: 16,
            decelerationRate: "normal" as const,
            bounces: false,
            alwaysBounceHorizontal: false,
            alwaysBounceVertical: false,
        },
    };

    const currentScrollConfig =
        Platform.OS === "ios" ? scrollConfig.ios : scrollConfig.android;



    if (selectedEmojis.length === 0) {
        return (
            <View style={styles.container} testID={`${testID}.container`}>
                <View style={styles.header}>
                    <Text style={styles.title}>Selected (0)</Text>
                </View>
                <Text style={styles.emptyText}>Tap emojis to select them</Text>
                {__DEV__ && (
                    <Text style={styles.debugText}>
                        Debug: Container: {scrollDiagnostics.containerWidth}px,
                        Content: {scrollDiagnostics.contentWidth}px,
                        Can Scroll: {scrollDiagnostics.canScroll ? 'Yes' : 'No'}
                    </Text>
                )}
            </View>
        );
    }

    return (
        <View style={styles.container} testID={`${testID}.container`}>
            <View style={styles.header}>
                <Text style={styles.title}>
                    Selected ({selectedEmojis.length})
                </Text>
                <TouchableOpacity
                    style={styles.doneButton}
                    onPress={handleDone}
                    testID={`${testID}.done_button`}
                >
                    <Text style={styles.doneButtonText}>Done</Text>
                </TouchableOpacity>
                {__DEV__ && (
                    <>
                        <TouchableOpacity
                            style={[styles.doneButton, { backgroundColor: 'orange', marginLeft: 8 }]}
                            onPress={runScrollTests}
                            testID={`${testID}.test_button`}
                        >
                            <Text style={styles.doneButtonText}>Test</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            style={[styles.doneButton, { backgroundColor: 'purple', marginLeft: 8 }]}
                            onPress={toggleScrollViewType}
                            testID={`${testID}.toggle_button`}
                        >
                            <Text style={styles.doneButtonText}>
                                {useBottomSheetScrollView ? 'BS' : 'SV'}
                            </Text>
                        </TouchableOpacity>
                    </>
                )}
            </View>
            <View
                style={styles.scrollContainer}
                onLayout={handleContainerLayout}
            >
                <ScrollView
                    ref={scrollViewRef}
                    horizontal
                    showsHorizontalScrollIndicator={true}
                    contentContainerStyle={styles.scrollContent}
                    testID={`${testID}.scroll_view`}
                    bounces={currentScrollConfig.bounces}
                    scrollEventThrottle={
                        currentScrollConfig.scrollEventThrottle
                    }
                    decelerationRate={currentScrollConfig.decelerationRate}
                    nestedScrollEnabled={
                        currentScrollConfig.nestedScrollEnabled
                    }
                    alwaysBounceHorizontal={
                        currentScrollConfig.alwaysBounceHorizontal
                    }
                    alwaysBounceVertical={
                        currentScrollConfig.alwaysBounceVertical
                    }
                    removeClippedSubviews={false}
                    keyboardShouldPersistTaps="handled"
                    directionalLockEnabled={true}
                    scrollEnabled={true}
                    pagingEnabled={false}
                    disableIntervalMomentum={false}
                    onScroll={handleScroll}
                    onScrollBeginDrag={handleScrollBeginDrag}
                    onScrollEndDrag={handleScrollEndDrag}
                    onMomentumScrollBegin={handleMomentumScrollBegin}
                    onMomentumScrollEnd={handleMomentumScrollEnd}
                    onContentSizeChange={handleContentSizeChange}
                >
                    {selectedEmojis.map((emoji) => (
                        <View
                            key={emoji.id}
                            style={styles.emojiItem}
                            testID={`${testID}.emoji_item.${emoji.id}`}
                        >
                            <Text style={styles.emojiCharacter}>
                                {emoji.character}
                            </Text>
                            <TouchableOpacity
                                style={styles.removeButton}
                                onPress={() => handleRemoveEmoji(emoji.id)}
                                testID={`${testID}.remove_button.${emoji.id}`}
                                hitSlop={{
                                    top: 8,
                                    bottom: 8,
                                    left: 8,
                                    right: 8,
                                }}
                                activeOpacity={0.7}
                            >
                                <CompassIcon
                                    name="close"
                                    size={10}
                                    style={styles.removeIcon}
                                />
                            </TouchableOpacity>
                        </View>
                    ))}
                </ScrollView>
                {__DEV__ && (
                    <Text style={styles.debugText}>
                        Debug: Container: {scrollDiagnostics.containerWidth}px,
                        Content: {scrollDiagnostics.contentWidth}px,
                        Position: {scrollDiagnostics.scrollPosition.toFixed(1)}px,
                        Can Scroll: {scrollDiagnostics.canScroll ? 'Yes' : 'No'}
                    </Text>
                )}
            </View>
        </View>
    );
};

export default EmojiPickerPreview;
